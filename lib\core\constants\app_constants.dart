class AppConstants {
  // API Constants
  static const String baseUrl = 'http://195.181.245.107/wesell/api';
  static const String loginEndpoint = '/security/login';
  static const String registerEndpoint = '/users/register';

  // Timeout Constants
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String isAdmin = 'Bret';
  
  // App Info
  static const String appName = 'WeSell';
  static const String appVersion = '1.0.0';
  
  // Validation Constants
  static const int minUsernameLength = 3;
  static const int minPasswordLength = 6;
  static const int maxUsernameLength = 50;
  static const int maxPasswordLength = 100;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double buttonHeight = 48.0;
  static const double textFieldHeight = 56.0;
  
  // Animation Constants
  static const int defaultAnimationDuration = 300;
  static const int fastAnimationDuration = 150;
  static const int slowAnimationDuration = 500;
  
  // Error Messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your connection.';
  static const String timeoutErrorMessage = 'Request timeout. Please try again.';
  static const String serverErrorMessage = 'Server error. Please try again later.';

  //DioError
    static const requestTimeout  = 'Request timeout. Please try again.';
    static const serverError  = 'Server error occurred';
    static const unknownErrorMessage = 'Unknown error occurred';
    static const requestCancelled = 'Request was cancelled';
    static const authenticationError = 'Authentication failed';
    static const authorizationError = 'Authorization failed';
    static const resourceNotFound = 'Resource not found';


  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String logoutSuccessMessage = 'Logout successful!';

  //conditions string
  static const String logout = 'logout';
  
  // Regex Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  static const String usernamePattern = r'^[a-zA-Z0-9_]{3,50}$';
  
  // HTTP Status Codes
  static const int httpOk = 200;
  static const int httpCreated = 201;
  static const int httpBadRequest = 400;
  static const int httpUnauthorized = 401;
  static const int httpForbidden = 403;
  static const int httpNotFound = 404;
  static const int httpInternalServerError = 500;
  static const int httpBadGateway = 502;
  static const int httpServiceUnavailable = 503;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache
  static const int cacheExpirationTime = 3600; // 1 hour in seconds
  static const int maxCacheSize = 100; // Maximum number of cached items

  // Task Status
  static const String taskStatusPending = 'pending';
  static const String taskStatusCompleted = 'completed';

  // Field Names
  static const String fieldName = 'Name';
  static const String fieldTitle = 'Title';
  static const String fieldPhone = 'Phone';
  static const String fieldAddress = 'Address';
  static const String fieldDescription = 'Description';
  static const String fieldStatus = 'Status';

  // Language Codes
  static const String langEn = 'en';
  static const String langAr = 'ar';

  // HTTP Headers
  static const String contentTypeHeader = 'Content-Type';
  static const String acceptHeader = 'Accept';
  static const String applicationJson = 'application/json';

}
