import 'dart:io';
import 'package:path/path.dart' as path;

class FileUtils {
  static const int maxFileSizeInBytes = 10 * 1024 * 1024; // 10 MB
  static const List<String> allowedExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.pdf'
  ];

  static bool isValidFileSize(File file) {
    return file.lengthSync() <= maxFileSizeInBytes;
  }

  static bool isValidFileFormat(String fileName) {
    final extension = path.extension(fileName).toLowerCase();
    return allowedExtensions.contains(extension);
  }

  static String? validateFile(File file, String fileName) {
    if (!isValidFileFormat(fileName)) {
      return 'Only JPG, PNG, JPEG, GIF, and PDF files are allowed';
    }
    
    if (!isValidFileSize(file)) {
      return 'File size must be less than 10 MB';
    }
    
    return null; // File is valid
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
