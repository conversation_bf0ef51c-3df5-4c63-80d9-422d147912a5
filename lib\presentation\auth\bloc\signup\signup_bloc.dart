import 'package:bloc/bloc.dart';
import 'package:wesell/domain/usecases/register_usecase.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_event.dart';
import 'package:wesell/presentation/auth/bloc/signup/signup_state.dart';


class SignupBloc extends Bloc<SignupEvent, SignupState> {
  final RegisterUseCase registerUseCase;
  SignupBloc({required this.registerUseCase}) : super(SignupInitial()) {
    on<SignupSubmitted>(_onSignupSubmitted);
  }

  Future<void> _onSignupSubmitted(SignupSubmitted event, Emitter<SignupState> emit) async {
    emit(SignupLoading());
    try {
      final result = await registerUseCase(RegisterParams(
        firstName: event.firstName,
        lastName: event.lastName,
        email: event.email,
        password: event.password,
        type: event.type,
        companyLegalName: event.companyLegalName,
        crNumber: event.crNumber,
        investedCapital: event.investedCapital,
        investedCapitalUnit: event.investedCapitalUnit,
        companySize: event.companySize,
        licenseDocument: event.licenseDocument,
        crDocument: event.crDocument,
      ));
      result.fold(
        (failure) => emit(SignupFailure(failure.message)),
        (response) => emit(SignupSuccess(response.message)),
      );
    } catch (e) {
      emit(SignupFailure(e.toString()));
    }
  }
}
