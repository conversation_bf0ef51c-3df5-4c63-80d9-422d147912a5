import 'package:equatable/equatable.dart';

abstract class SignupEvent extends Equatable {
  const SignupEvent();
  @override
  List<Object?> get props => [];
}

class SignupSubmitted extends SignupEvent {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String type;
  // Merchant fields (optional)
  final String? companyLegalName;
  final String? crNumber;
  final int? investedCapital;
  final String? investedCapitalUnit;
  final String? companySize;
  final String? licenseDocument;
  final String? crDocument;

  const SignupSubmitted({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    required this.type,
    this.companyLegalName,
    this.crNumber,
    this.investedCapital,
    this.investedCapitalUnit,
    this.companySize,
    this.licenseDocument,
    this.crDocument,
  });

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        password,
        type,
        companyLegalName,
        crNumber,
        investedCapital,
        investedCapitalUnit,
        companySize,
        licenseDocument,
        crDocument
      ];
}
