import 'package:equatable/equatable.dart';

abstract class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

class LoginSubmitted extends LoginEvent {
  final String email;
  final String password;

  const LoginSubmitted({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

class LogoutRequested extends LoginEvent {
  final String? sessionId;
  const LogoutRequested({this.sessionId});

  @override
  List<Object?> get props => [sessionId];
}

class CheckLoginStatus extends LoginEvent {
  const CheckLoginStatus();
}
